.gray-page[data-v-03a869a4] {
    background-color: #f9f9f9
}

.bottom-placeholder[data-v-03a869a4] {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.pointer[data-v-03a869a4] {
    cursor: pointer
}

@-webkit-keyframes scale-03a869a4 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@keyframes scale-03a869a4 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

html[data-v-03a869a4] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html body[data-v-03a869a4] {
    background-color: #f9f9f9;
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
    user-scalable: no
}

@media screen and (min-width: 750PX) {
    html[data-v-03a869a4]::-webkit-scrollbar {
        width:.18667rem;
        height: .18667rem
    }

    html[data-v-03a869a4]::-webkit-scrollbar-thumb {
        border-radius: .21333rem;
        background: #dddee0!important;
        margin-bottom: .26667rem
    }

    html body[data-v-03a869a4] {
        width: 750PX!important;
        margin: 0 auto
    }

    .van-popup--bottom[data-v-03a869a4],.van-tabbar[data-v-03a869a4] {
        width: 750PX!important;
        left: 50%!important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

[data-v-03a869a4] {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

[data-v-03a869a4],[data-v-03a869a4]:after,[data-v-03a869a4]:before {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

a[data-v-03a869a4]:active,a[data-v-03a869a4]:focus {
    outline: none
}

a[data-v-03a869a4],a[data-v-03a869a4]:focus,a[data-v-03a869a4]:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none
}

div[data-v-03a869a4]:focus {
    outline: none
}

.button[data-v-03a869a4] {
    cursor: pointer
}

img[data-v-03a869a4] {
    pointer-events: none;
    max-width: 100%
}

#nprogress .bar[data-v-03a869a4] {
    height: .08rem!important;
    background-color: #ff5d7d!important
}

#nprogress .bar .peg[data-v-03a869a4] {
    -webkit-box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d;
    box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d
}

#nprogress .spinner-icon[data-v-03a869a4] {
    width: .58667rem!important;
    height: .58667rem!important;
    border: .08rem solid transparent!important;
    border-left-color: #ff5d7d!important;
    border-top-color: #ff5d7d!important
}

p[data-v-03a869a4] {
    display: block;
    -webkit-margin-before: 1em;
    margin-block-start:1em;-webkit-margin-after: 1em;
    margin-block-end:1em;-webkit-margin-start: 0;
    margin-inline-start:0;-webkit-margin-end: 0;
    margin-inline-end:0}

.cnt2[data-v-03a869a4] {
    min-height: 100vh;
    width: 100%;
    overflow: hidden;
    font-size: .37333rem;
    background: #4398b5
}

.mbti_top[data-v-03a869a4] {
    text-align: center;
    color: #fff;
    font-size: .53333rem
}

.mbti_top p[data-v-03a869a4] {
    margin: 0
}

.mbti_top img[data-v-03a869a4] {
    width: 100%;
    display: block;
    margin: 0 auto
}

.mbti_top .mbti_title2[data-v-03a869a4] {
    margin-top: -.26667rem;
    font-size: .48rem
}

.mbti_top .mbti_title[data-v-03a869a4] {
    margin-top: 1.06667rem;
    color: #ffeb3b;
    font-size: .53333rem;
    font-weight: 500;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.mbti_top .mbti_title .ioc_left[data-v-03a869a4] {
    margin-right: .13333rem
}

.mbti_top .mbti_title .ioc_right[data-v-03a869a4] {
    margin-left: .13333rem
}

.two[data-v-03a869a4] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    margin-top: .26667rem;
    width: 100%
}

.two .cur[data-v-03a869a4] {
    border: .10667rem solid #ff5d7d!important;
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.two .cont[data-v-03a869a4] {
    padding: .4rem .4rem .26667rem .4rem;
    border-radius: .26667rem;
    width: 100%;
    border: .08rem solid rgba(255,0,0,0);
    -webkit-box-shadow: 0 .08rem .3rem 0 #207997;
    box-shadow: 0 .08rem .3rem 0 #207997;
    background: #fff
}

.cont1[data-v-03a869a4],.two .cont[data-v-03a869a4] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center
}

.cont1[data-v-03a869a4] {
    width: 37%
}

.cout_num[data-v-03a869a4] {
    font-size: .37333rem;
    margin-top: .32rem;
    color: #f7d808;
    font-weight: 600
}

.cout_text[data-v-03a869a4] {
    font-size: .37333rem;
    margin-top: .05333rem;
    color: #fff;
    font-weight: 600
}

.cur2[data-v-03a869a4] {
    display: none
}

.two .img1[data-v-03a869a4] {
    display: block;
    background: #ff5d7d;
    width: .53333rem;
    height: .53333rem;
    border-bottom-right-radius: .13333rem;
    border-top-left-radius: .13333rem;
    position: absolute;
    top: -.02667rem;
    left: -.02667rem
}

.two .img1 img[data-v-03a869a4] {
    pointer-events: none;
    max-width: 90%
}

.two .cont .img[data-v-03a869a4] {
    width: 100%
}

.two .cont .bt1[data-v-03a869a4] {
    font-size: .45333rem;
    margin-top: .16rem;
    margin-bottom: .10667rem
}

.bt2[data-v-03a869a4] {
    font-size: .26667rem;
    color: #bebebe;
    margin-top: .05333rem
}

.two .cont p[data-v-03a869a4] {
    color: #999;
    letter-spacing: 0
}

.page_detail .detail_title_ydrated[data-v-03a869a4] {
    text-align: center;
    font-weight: 700;
    font-size: .48rem;
    margin-bottom: .26667rem
}

.page_detail .detail_txt___3QorP[data-v-03a869a4] {
    line-height: .61333rem
}

.page_detail[data-v-03a869a4] {
    width: 90%;
    background-color: #207997;
    border-radius: .26667rem;
    color: #fff;
    margin: 0 auto;
    margin-top: .13333rem;
    padding: .4rem
}

.page_detail[data-v-03a869a4] h5 {
    text-align: center;
    font-weight: 700;
    font-size: .48rem;
    margin-bottom: .26667rem
}

.page_detail[data-v-03a869a4] p {
    line-height: .61333rem;
    font-size: .37333rem
}

.mbtibtn[data-v-03a869a4] {
    margin-left: 50%;
    margin-top: .93333rem;
    -webkit-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background: #ff5d7d;
    -webkit-box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    border-radius: 1.2rem;
    line-height: 1.2rem;
    font-weight: 700;
    font-size: .48rem;
    height: 1.06667rem;
    width: 4.8rem;
    border-radius: 1.06667rem;
    color: #fff;
    line-height: 1.06667rem;
    text-align: center;
    letter-spacing: .13333rem
}

.tips[data-v-03a869a4] {
    padding: .13333rem 0 0 0;
    color: #fff;
    font-size: .37333rem
}

.tips[data-v-03a869a4] img {
    width: 100%;
    display: block;
    padding: 0;
    margin: 0
}

.tips2[data-v-03a869a4] {
    margin-top: -.13333rem;
    padding-bottom: .13333rem;
    color: #fff;
    font-size: .32rem;
    text-align: center
}

@-webkit-keyframes scaleAnimation-data-v-9a11e5a2-03a869a4 {
    0% {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }

    50% {
        -webkit-transform: translate(-50%,-50%) scale(1.1);
        transform: translate(-50%,-50%) scale(1.1)
    }

    to {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }
}

@keyframes scaleAnimation-data-v-9a11e5a2-03a869a4 {
    0% {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }

    50% {
        -webkit-transform: translate(-50%,-50%) scale(1.1);
        transform: translate(-50%,-50%) scale(1.1)
    }

    to {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }
}

.m[data-v-03a869a4] {
    margin-top: .13333rem;
    width: 100%
}

.m img[data-v-03a869a4] {
    width: 100%;
    display: block;
    padding: 0;
    margin: 0
}

.gray-page[data-v-54b68ba8] {
    background-color: #f9f9f9
}

.bottom-placeholder[data-v-54b68ba8] {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.pointer[data-v-54b68ba8] {
    cursor: pointer
}

@-webkit-keyframes scale-54b68ba8 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@keyframes scale-54b68ba8 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

html[data-v-54b68ba8] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html body[data-v-54b68ba8] {
    background-color: #f9f9f9;
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
    user-scalable: no
}

@media screen and (min-width: 750PX) {
    html[data-v-54b68ba8]::-webkit-scrollbar {
        width:.18667rem;
        height: .18667rem
    }

    html[data-v-54b68ba8]::-webkit-scrollbar-thumb {
        border-radius: .21333rem;
        background: #dddee0!important;
        margin-bottom: .26667rem
    }

    html body[data-v-54b68ba8] {
        width: 750PX!important;
        margin: 0 auto
    }

    .van-popup--bottom[data-v-54b68ba8],.van-tabbar[data-v-54b68ba8] {
        width: 750PX!important;
        left: 50%!important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

[data-v-54b68ba8] {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

[data-v-54b68ba8],[data-v-54b68ba8]:after,[data-v-54b68ba8]:before {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

a[data-v-54b68ba8]:active,a[data-v-54b68ba8]:focus {
    outline: none
}

a[data-v-54b68ba8],a[data-v-54b68ba8]:focus,a[data-v-54b68ba8]:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none
}

div[data-v-54b68ba8]:focus {
    outline: none
}

.button[data-v-54b68ba8] {
    cursor: pointer
}

img[data-v-54b68ba8] {
    pointer-events: none;
    max-width: 100%
}

#nprogress .bar[data-v-54b68ba8] {
    height: .08rem!important;
    background-color: #ff5d7d!important
}

#nprogress .bar .peg[data-v-54b68ba8] {
    -webkit-box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d;
    box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d
}

#nprogress .spinner-icon[data-v-54b68ba8] {
    width: .58667rem!important;
    height: .58667rem!important;
    border: .08rem solid transparent!important;
    border-left-color: #ff5d7d!important;
    border-top-color: #ff5d7d!important
}

p[data-v-54b68ba8] {
    display: block;
    -webkit-margin-before: 1em;
    margin-block-start:1em;-webkit-margin-after: 1em;
    margin-block-end:1em;-webkit-margin-start: 0;
    margin-inline-start:0;-webkit-margin-end: 0;
    margin-inline-end:0}

.mbtibtn[data-v-54b68ba8] {
    margin-left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #ff5d7d;
    -webkit-box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    line-height: 1.06667rem;
    text-align: center;
    font-size: .48rem;
    color: #fff;
    font-weight: 700;
    height: 1.06667rem;
    width: 4.8rem;
    border-radius: 1.06667rem;
    letter-spacing: .13333rem;
    margin-bottom: .08rem
}

.cnt2[data-v-54b68ba8] {
    height: 100vh;
    overflow-y: scroll;
    width: 100%;
    font-size: .37333rem
}

.cnt2[data-v-54b68ba8]::-webkit-scrollbar {
    width: .18667rem;
    height: .18667rem
}

.cnt2[data-v-54b68ba8]::-webkit-scrollbar-thumb {
    border-radius: .21333rem;
    background: #dddee0!important;
    margin-bottom: .26667rem
}

.mbti_cont[data-v-54b68ba8] {
    background-image: url(https://oss.1cece.top/goods/mbti/page05-title-bg.jpg);
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    width: 100%;
    padding-bottom: 7.33333rem;
    position: relative
}

.mbti_top[data-v-54b68ba8] {
    text-align: center;
    padding-top: .26667rem;
    color: #fff;
    font-size: .53333rem
}

.mbti_top p[data-v-54b68ba8] {
    margin: 0
}

.mbti_top img[data-v-54b68ba8] {
    width: 95%;
    display: block;
    margin: 0 auto
}

.mbti_top .mbti_title2[data-v-54b68ba8] {
    font-size: .42667rem;
    color: #5e5f63;
    font-weight: 500
}

.mbti_top .mbti_title[data-v-54b68ba8] {
    margin-top: .66667rem;
    color: #5f64e5;
    font-size: .53333rem;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.mbti_top .mbti_title .ioc_left[data-v-54b68ba8] {
    margin-right: .13333rem
}

.mbti_top .mbti_title .ioc_right[data-v-54b68ba8] {
    margin-left: .13333rem
}

.two[data-v-54b68ba8] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    margin-top: .53333rem;
    width: 100%
}

.two .cur[data-v-54b68ba8] {
    border: .10667rem solid #717fe9!important;
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.two .cont[data-v-54b68ba8] {
    padding: .26667rem .4rem;
    border-radius: .26667rem;
    width: 37%;
    border: .10667rem solid rgba(255,0,0,0);
    text-align: center;
    -webkit-box-shadow: 0 0 .3rem 0 #ddd;
    box-shadow: 0 0 .3rem 0 #ddd;
    background: #fff;
    position: relative
}

.cur2[data-v-54b68ba8] {
    display: none
}

.two .img1[data-v-54b68ba8] {
    display: block;
    background: #717fe9;
    width: .53333rem;
    height: .53333rem;
    border-bottom-right-radius: .13333rem;
    border-top-left-radius: .13333rem;
    position: absolute;
    top: -.02667rem;
    left: -.02667rem
}

.two .img1 img[data-v-54b68ba8] {
    pointer-events: none;
    max-width: 70%;
    margin-top: .08rem
}

.two .cont .img[data-v-54b68ba8] {
    width: 100%
}

.two .cont .bt1[data-v-54b68ba8] {
    font-size: .42667rem;
    margin-top: .16rem
}

.bt1-tips[data-v-54b68ba8] {
    font-size: .37333rem;
    color: #a6a6a8;
    line-height: .37333rem;
    margin-top: .26667rem
}

.two .cont p[data-v-54b68ba8] {
    color: #999;
    letter-spacing: 0
}

.tips[data-v-54b68ba8] {
    font-size: .37333rem
}

.tips[data-v-54b68ba8] img {
    margin-top: -.13333rem
}

.footer[data-v-54b68ba8] {
    height: 1.33333rem;
    position: fixed;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: 0;
    width: 100%;
    background-color: #fff;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: transparent;
    padding-bottom: .66667rem;
    cursor: pointer
}

@media screen and (min-width: 750PX) {
    .footer[data-v-54b68ba8] {
        width:750PX
    }
}

.button[data-v-54b68ba8] {
    width: 7.46667rem!important;
    height: 1.06667rem;
    text-align: center;
    line-height: 1.06667rem;
    border-radius: 1.06667rem;
    width: 100%;
    color: #fff;
    -webkit-animation: scale-54b68ba8 3s infinite;
    animation: scale-54b68ba8 3s infinite;
    font-size: .48rem;
    background-color: #ff5d7d
}

/* .mbti_cont__button[data-v-54b68ba8] {
    position: absolute;
    bottom: 0;
    left: 1.28rem
} */

.gray-page[data-v-b1674af4] {
    background-color: #f9f9f9
}

.bottom-placeholder[data-v-b1674af4] {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.pointer[data-v-b1674af4] {
    cursor: pointer
}

@-webkit-keyframes scale-b1674af4 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@keyframes scale-b1674af4 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

html[data-v-b1674af4] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html body[data-v-b1674af4] {
    background-color: #f9f9f9;
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
    user-scalable: no
}

@media screen and (min-width: 750PX) {
    html[data-v-b1674af4]::-webkit-scrollbar {
        width:.18667rem;
        height: .18667rem
    }

    html[data-v-b1674af4]::-webkit-scrollbar-thumb {
        border-radius: .21333rem;
        background: #dddee0!important;
        margin-bottom: .26667rem
    }

    html body[data-v-b1674af4] {
        width: 750PX!important;
        margin: 0 auto
    }

    .van-popup--bottom[data-v-b1674af4],.van-tabbar[data-v-b1674af4] {
        width: 750PX!important;
        left: 50%!important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

[data-v-b1674af4] {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

[data-v-b1674af4],[data-v-b1674af4]:after,[data-v-b1674af4]:before {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

a[data-v-b1674af4]:active,a[data-v-b1674af4]:focus {
    outline: none
}

a[data-v-b1674af4],a[data-v-b1674af4]:focus,a[data-v-b1674af4]:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none
}

div[data-v-b1674af4]:focus {
    outline: none
}

.button[data-v-b1674af4] {
    cursor: pointer
}

img[data-v-b1674af4] {
    pointer-events: none;
    max-width: 100%
}

#nprogress .bar[data-v-b1674af4] {
    height: .08rem!important;
    background-color: #ff5d7d!important
}

#nprogress .bar .peg[data-v-b1674af4] {
    -webkit-box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d;
    box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d
}

#nprogress .spinner-icon[data-v-b1674af4] {
    width: .58667rem!important;
    height: .58667rem!important;
    border: .08rem solid transparent!important;
    border-left-color: #ff5d7d!important;
    border-top-color: #ff5d7d!important
}

p[data-v-b1674af4] {
    display: block;
    -webkit-margin-before: 1em;
    margin-block-start:1em;-webkit-margin-after: 1em;
    margin-block-end:1em;-webkit-margin-start: 0;
    margin-inline-start:0;-webkit-margin-end: 0;
    margin-inline-end:0}

.cnt2[data-v-b1674af4] {
    min-height: 100vh;
    width: 100%;
    overflow: hidden;
    background-image: linear-gradient(145deg,#5e80dd,#704cb5);
    font-size: .37333rem
}

.mbti_cont[data-v-b1674af4] {
    background: url(../../static-05301403/img/page01-bg.822d9d6a.png) top no-repeat;
    width: 100%;
    height: auto;
    background-size: cover;
    overflow: hidden;
    min-height: 100vh
}

.mbti_top[data-v-b1674af4] {
    text-align: center;
    color: #fff;
    font-size: .53333rem
}

.mbti_top p[data-v-b1674af4] {
    margin: 0
}

.mbti_top img[data-v-b1674af4] {
    width: 95%;
    display: block;
    margin: 0 auto
}

.mbti_top .mbti_title2[data-v-b1674af4] {
    margin-top: -.26667rem;
    font-size: .48rem
}

.mbti_top .mbti_title[data-v-b1674af4] {
    margin-top: 1.06667rem;
    color: #ffeb3b;
    font-size: .53333rem;
    font-weight: 500;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.mbti_top .mbti_title .ioc_left[data-v-b1674af4] {
    margin-right: .13333rem
}

.mbti_top .mbti_title .ioc_right[data-v-b1674af4] {
    margin-left: .13333rem
}

.two[data-v-b1674af4] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    margin-top: .53333rem;
    width: 100%
}

.two .cur[data-v-b1674af4] {
    border: .10667rem solid #ff5d7d!important;
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.two .cont[data-v-b1674af4] {
    padding: .4rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: .26667rem;
    width: 37%;
    border: .08rem solid rgba(255,0,0,0);
    text-align: center;
    -webkit-box-shadow: 0 .08rem .3rem 0 #5934a0;
    box-shadow: 0 .08rem .3rem 0 #5934a0;
    background: #fff
}

.cur2[data-v-b1674af4] {
    display: none
}

.two .img1[data-v-b1674af4] {
    display: block;
    background: #ff5d7d;
    width: .53333rem;
    height: .53333rem;
    border-bottom-right-radius: .13333rem;
    border-top-left-radius: .13333rem;
    position: absolute;
    top: -.02667rem;
    left: -.02667rem
}

.two .img1 img[data-v-b1674af4] {
    pointer-events: none;
    max-width: 90%
}

.two .cont .img[data-v-b1674af4] {
    width: 100%
}

.two .cont .bt1[data-v-b1674af4] {
    font-size: .45333rem;
    margin-top: .16rem
}

.two .cont p[data-v-b1674af4] {
    color: #999;
    letter-spacing: 0
}

.page_detail .detail_title_ydrated[data-v-b1674af4] {
    text-align: center;
    font-weight: 700;
    font-size: .48rem;
    margin-bottom: .26667rem
}

.page_detail .detail_txt___3QorP[data-v-b1674af4] {
    line-height: .61333rem
}

.page_detail[data-v-b1674af4] {
    width: 90%;
    background-color: rgba(86,66,184,.9);
    border-radius: .26667rem;
    color: #fff;
    margin: 0 auto;
    margin-top: .26667rem;
    padding: .4rem
}

.page_detail[data-v-b1674af4] h5 {
    text-align: center;
    font-weight: 700;
    font-size: .48rem;
    margin-bottom: .26667rem
}

.page_detail[data-v-b1674af4] p {
    line-height: .61333rem;
    font-size: .37333rem
}

.mbtibtn[data-v-b1674af4] {
    margin-left: 50%;
    margin-top: 1.33333rem;
    -webkit-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background: #ff5d7d;
    -webkit-box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    border-radius: 1.2rem;
    line-height: 1.2rem;
    font-weight: 700;
    font-size: .48rem;
    height: 1.06667rem;
    width: 4.8rem;
    border-radius: 1.06667rem;
    color: #fff;
    line-height: 1.06667rem;
    text-align: center;
    letter-spacing: .13333rem
}

.tips[data-v-b1674af4] {
    padding: .26667rem .53333rem .4rem;
    color: #fff;
    font-size: .37333rem
}

.tips2[data-v-b1674af4] {
    margin-top: -.26667rem;
    padding-bottom: .53333rem;
    color: #fff;
    font-size: .32rem;
    text-align: center
}

@-webkit-keyframes scaleAnimation-data-v-9a11e5a2-b1674af4 {
    0% {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }

    50% {
        -webkit-transform: translate(-50%,-50%) scale(1.1);
        transform: translate(-50%,-50%) scale(1.1)
    }

    to {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }
}

@keyframes scaleAnimation-data-v-9a11e5a2-b1674af4 {
    0% {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }

    50% {
        -webkit-transform: translate(-50%,-50%) scale(1.1);
        transform: translate(-50%,-50%) scale(1.1)
    }

    to {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }
}

.m[data-v-b1674af4] {
    margin-top: -.4rem;
    width: 100%
}

.m img[data-v-b1674af4] {
    width: 100%;
    display: block;
    padding: 0;
    margin: 0
}

.gray-page[data-v-2c9cfb8d] {
    background-color: #f9f9f9
}

.bottom-placeholder[data-v-2c9cfb8d] {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.pointer[data-v-2c9cfb8d] {
    cursor: pointer
}

@-webkit-keyframes scale-2c9cfb8d {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@keyframes scale-2c9cfb8d {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

html[data-v-2c9cfb8d] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html body[data-v-2c9cfb8d] {
    background-color: #f9f9f9;
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
    user-scalable: no
}

@media screen and (min-width: 750PX) {
    html[data-v-2c9cfb8d]::-webkit-scrollbar {
        width:.18667rem;
        height: .18667rem
    }

    html[data-v-2c9cfb8d]::-webkit-scrollbar-thumb {
        border-radius: .21333rem;
        background: #dddee0!important;
        margin-bottom: .26667rem
    }

    html body[data-v-2c9cfb8d] {
        width: 750PX!important;
        margin: 0 auto
    }

    .van-popup--bottom[data-v-2c9cfb8d],.van-tabbar[data-v-2c9cfb8d] {
        width: 750PX!important;
        left: 50%!important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

[data-v-2c9cfb8d] {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

[data-v-2c9cfb8d],[data-v-2c9cfb8d]:after,[data-v-2c9cfb8d]:before {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

a[data-v-2c9cfb8d]:active,a[data-v-2c9cfb8d]:focus {
    outline: none
}

a[data-v-2c9cfb8d],a[data-v-2c9cfb8d]:focus,a[data-v-2c9cfb8d]:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none
}

div[data-v-2c9cfb8d]:focus {
    outline: none
}

.button[data-v-2c9cfb8d] {
    cursor: pointer
}

img[data-v-2c9cfb8d] {
    pointer-events: none;
    max-width: 100%
}

#nprogress .bar[data-v-2c9cfb8d] {
    height: .08rem!important;
    background-color: #ff5d7d!important
}

#nprogress .bar .peg[data-v-2c9cfb8d] {
    -webkit-box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d;
    box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d
}

#nprogress .spinner-icon[data-v-2c9cfb8d] {
    width: .58667rem!important;
    height: .58667rem!important;
    border: .08rem solid transparent!important;
    border-left-color: #ff5d7d!important;
    border-top-color: #ff5d7d!important
}

p[data-v-2c9cfb8d] {
    display: block;
    -webkit-margin-before: 1em;
    margin-block-start:1em;-webkit-margin-after: 1em;
    margin-block-end:1em;-webkit-margin-start: 0;
    margin-inline-start:0;-webkit-margin-end: 0;
    margin-inline-end:0}

.mbtibtn[data-v-2c9cfb8d] {
    margin-left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #ff5d7d;
    -webkit-box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    line-height: 1.06667rem;
    text-align: center;
    font-size: .48rem;
    color: #fff;
    font-weight: 700;
    height: 1.06667rem;
    width: 4.8rem;
    border-radius: 1.06667rem;
    letter-spacing: .13333rem;
    margin-bottom: .08rem
}

.cnt2[data-v-2c9cfb8d] {
    height: var(--container-min-height);
    overflow-y: auto;
    width: 100%;
    font-size: .37333rem
}

.cnt2[data-v-2c9cfb8d]::-webkit-scrollbar {
    width: .18667rem;
    height: .18667rem
}

.cnt2[data-v-2c9cfb8d]::-webkit-scrollbar-thumb {
    border-radius: .21333rem;
    background: #dddee0!important;
    margin-bottom: .26667rem
}

.mbti_cont[data-v-2c9cfb8d] {
    width: 100%;
    position: relative
}

.mbti_top[data-v-2c9cfb8d] {
    text-align: center;
    color: #fff;
    font-size: .53333rem;
    position: relative
}

.mbti_top p[data-v-2c9cfb8d] {
    margin: 0
}

.mbti_top img[data-v-2c9cfb8d] {
    display: block;
    width: 100%
}

.mbti_top .mbti_title2[data-v-2c9cfb8d] {
    font-size: .42667rem;
    color: #5e5f63;
    font-weight: 500
}

.mbti_top .mbti_title2[data-v-2c9cfb8d] img {
    width: 100%
}

.mbti_top .mbti_title[data-v-2c9cfb8d] {
    margin-top: .66667rem;
    color: #5f64e5;
    font-size: .53333rem;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.mbti_top .mbti_title .ioc_left[data-v-2c9cfb8d] {
    margin-right: .13333rem
}

.mbti_top .mbti_title .ioc_right[data-v-2c9cfb8d] {
    margin-left: .13333rem
}

.mbti_top .mbti_top_two[data-v-2c9cfb8d] {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    min-height: 10.4rem
}

.two[data-v-2c9cfb8d] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    margin-top: .42667rem;
    width: 100%
}

.two .cur[data-v-2c9cfb8d] {
    border: .10667rem solid #717fe9;
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.two .cont[data-v-2c9cfb8d] {
    border-radius: .26667rem;
    width: 37%;
    border: .10667rem solid #fff;
    text-align: center;
    -webkit-box-shadow: 0 0 .3rem 0 #ddd;
    box-shadow: 0 0 .3rem 0 #ddd;
    background: #fff;
    position: relative;
    padding: .05333rem
}

.two .img1[data-v-2c9cfb8d] {
    background-color: #717fe9;
    width: .53333rem;
    height: .53333rem;
    border-bottom-right-radius: .13333rem;
    border-top-left-radius: .13333rem;
    position: absolute;
    top: -.02667rem;
    left: -.02667rem
}

.two .img1 img[data-v-2c9cfb8d] {
    pointer-events: none;
    max-width: 70%;
    margin-top: .08rem;
    margin-left: .05333rem
}

.two .cont .img[data-v-2c9cfb8d] {
    width: 100%
}

.two .cont .bt1[data-v-2c9cfb8d] {
    font-size: .42667rem;
    margin-top: .16rem
}

.bt1-tips[data-v-2c9cfb8d] {
    font-size: .37333rem;
    color: #a6a6a8;
    line-height: .37333rem;
    margin-top: .26667rem
}

.two .cont p[data-v-2c9cfb8d] {
    color: #999;
    letter-spacing: 0
}

.tips[data-v-2c9cfb8d] {
    font-size: .37333rem
}

.tips[data-v-2c9cfb8d] img {
    margin-top: -.13333rem
}

.footer[data-v-2c9cfb8d] {
    height: 1.33333rem;
    position: fixed;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: 0;
    width: 100%;
    background-color: #fff;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-color: transparent;
    padding-bottom: .66667rem;
    cursor: pointer
}

@media screen and (min-width: 750PX) {
    .footer[data-v-2c9cfb8d] {
        width:750PX
    }
}

.button[data-v-2c9cfb8d] {
    width: 7.46667rem!important;
    height: 1.06667rem;
    text-align: center;
    line-height: 1.06667rem;
    border-radius: 1.06667rem;
    width: 100%;
    color: #fff;
    -webkit-animation: scale-2c9cfb8d 3s infinite;

    animation: scale-2c9cfb8d 3s infinite;

    font-size: .48rem;
    background-color: #ff5d7d
}

/* .mbti_cont__button[data-v-2c9cfb8d] {
    position: absolute;
    bottom: .26667rem;
    left: 1.28rem
} */

.gray-page[data-v-74a0d1d6] {
    background-color: #f9f9f9
}

.bottom-placeholder[data-v-74a0d1d6] {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.pointer[data-v-74a0d1d6] {
    cursor: pointer
}

@-webkit-keyframes scale-74a0d1d6 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@keyframes scale-74a0d1d6 {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

html[data-v-74a0d1d6] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html body[data-v-74a0d1d6] {
    background-color: #f9f9f9;
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
    user-scalable: no
}

@media screen and (min-width: 750PX) {
    html[data-v-74a0d1d6]::-webkit-scrollbar {
        width:.18667rem;
        height: .18667rem
    }

    html[data-v-74a0d1d6]::-webkit-scrollbar-thumb {
        border-radius: .21333rem;
        background: #dddee0!important;
        margin-bottom: .26667rem
    }

    html body[data-v-74a0d1d6] {
        width: 750PX!important;
        margin: 0 auto
    }

    .van-popup--bottom[data-v-74a0d1d6],.van-tabbar[data-v-74a0d1d6] {
        width: 750PX!important;
        left: 50%!important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

[data-v-74a0d1d6] {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

[data-v-74a0d1d6],[data-v-74a0d1d6]:after,[data-v-74a0d1d6]:before {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

a[data-v-74a0d1d6]:active,a[data-v-74a0d1d6]:focus {
    outline: none
}

a[data-v-74a0d1d6],a[data-v-74a0d1d6]:focus,a[data-v-74a0d1d6]:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none
}

div[data-v-74a0d1d6]:focus {
    outline: none
}

.button[data-v-74a0d1d6] {
    cursor: pointer
}

img[data-v-74a0d1d6] {
    pointer-events: none;
    max-width: 100%
}

#nprogress .bar[data-v-74a0d1d6] {
    height: .08rem!important;
    background-color: #ff5d7d!important
}

#nprogress .bar .peg[data-v-74a0d1d6] {
    -webkit-box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d;
    box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d
}

#nprogress .spinner-icon[data-v-74a0d1d6] {
    width: .58667rem!important;
    height: .58667rem!important;
    border: .08rem solid transparent!important;
    border-left-color: #ff5d7d!important;
    border-top-color: #ff5d7d!important
}

p[data-v-74a0d1d6] {
    display: block;
    -webkit-margin-before: 1em;
    margin-block-start:1em;-webkit-margin-after: 1em;
    margin-block-end:1em;-webkit-margin-start: 0;
    margin-inline-start:0;-webkit-margin-end: 0;
    margin-inline-end:0}

.mbtibtn[data-v-74a0d1d6] {
    margin-left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #ff5d7d;
    -webkit-box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    box-shadow: 0 .21333rem .56rem 0 rgba(0,0,0,.19);
    line-height: 1.06667rem;
    text-align: center;
    font-size: .48rem;
    color: #fff;
    font-weight: 700;
    height: 1.06667rem;
    width: 4.8rem;
    border-radius: 1.06667rem;
    letter-spacing: .13333rem;
    margin-bottom: .08rem
}

.cnt2[data-v-74a0d1d6] {
    height: var(--container-min-height);
    width: 100%;
    font-size: .37333rem
}

.mbti_cont[data-v-74a0d1d6] {
    width: 100%;
    height: 18.4rem;
    position: relative;
    background: -webkit-gradient(linear,left top,left bottom,from(#fff),color-stop(43%,#e4f5ef),color-stop(73%,#8ccaac),to(#3aa577));
    background: linear-gradient(180deg,#fff,#e4f5ef 43%,#8ccaac 73%,#3aa577)
}

.mbti_top[data-v-74a0d1d6] {
    text-align: center;
    padding-top: 1.14667rem;
    color: #fff;
    font-size: .53333rem
}

.mbti_top p[data-v-74a0d1d6] {
    margin: 0
}

.mbti_top img[data-v-74a0d1d6] {
    width: 8.26667rem;
    display: block;
    margin: 0 auto
}

.two[data-v-74a0d1d6] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    margin-top: .88rem;
    width: 100%
}

.two .cont[data-v-74a0d1d6] {
    border-radius: .26667rem;
    width: 4.02667rem;
    text-align: center;
    position: relative
}

.two .cont .img[data-v-74a0d1d6] {
    width: 100%;
    height: 5.52rem;
    background-position: 0 0;
    background-size: cover;
    background-repeat: no-repeat
}

.two .cont .bt1[data-v-74a0d1d6] {
    position: absolute;
    top: .48rem;
    left: .4rem;
    font-family: Source Han Sans;
    font-size: .4rem;
    font-weight: 700;
    color: #848484
}

.two .cur .img[data-v-74a0d1d6] {
    background-position-x: -4.02667rem
}

.two .cur .bt1[data-v-74a0d1d6] {
    color: #3d3d3d!important
}

.bt1-tips[data-v-74a0d1d6] {
    font-size: .37333rem;
    color: #a6a6a8;
    line-height: .37333rem;
    margin-top: .26667rem
}

.tips[data-v-74a0d1d6] {
    position: relative;
    z-index: 1;
    margin: -4.05333rem .64rem 0
}

.tips[data-v-74a0d1d6] img {
    width: 100%
}

.button[data-v-74a0d1d6] {
    width: 6.93333rem;
    height: 1.57333rem;
    text-align: center;
    line-height: 1.57333rem;
    border-radius: 1.57333rem;
    -webkit-animation: scale-74a0d1d6 3s infinite;
    animation: scale-74a0d1d6 3s infinite;
    background: -webkit-gradient(linear,right top,left top,color-stop(11%,#34a97b),color-stop(88%,#83ddba));
    background: linear-gradient(270deg,#34a97b 11%,#83ddba 88%);
    border: .05333rem solid #fff;
    font-family: Source Han Sans;
    font-size: .64rem;
    font-weight: 700;
    letter-spacing: .05em;
    color: #fcfcfa;
    margin: 1.12rem auto 0
}

.copyright[data-v-74a0d1d6] {
    position: relative;
    z-index: 1;
    height: .42667rem
}

.footer-bottom[data-v-74a0d1d6] {
    height: .96rem
}

.gray-page[data-v-7330c60c] {
    background-color: #f9f9f9
}

.bottom-placeholder[data-v-7330c60c] {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.pointer[data-v-7330c60c] {
    cursor: pointer
}

@-webkit-keyframes scale-7330c60c {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@keyframes scale-7330c60c {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    25% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    75% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
}

@font-face {
    font-family: 优设标题;
    src: url(../../static-05301403/fonts/YouSheBiaoTiHei-2.1726685c.ttf)
}

html[data-v-7330c60c] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0,0,0,0)
}

html body[data-v-7330c60c] {
    background-color: #f9f9f9;
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y;
    user-scalable: no
}

@media screen and (min-width: 750PX) {
    html[data-v-7330c60c]::-webkit-scrollbar {
        width:.18667rem;
        height: .18667rem
    }

    html[data-v-7330c60c]::-webkit-scrollbar-thumb {
        border-radius: .21333rem;
        background: #dddee0!important;
        margin-bottom: .26667rem
    }

    html body[data-v-7330c60c] {
        width: 750PX!important;
        margin: 0 auto
    }

    .van-popup--bottom[data-v-7330c60c],.van-tabbar[data-v-7330c60c] {
        width: 750PX!important;
        left: 50%!important;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

[data-v-7330c60c] {
    margin: 0;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

[data-v-7330c60c],[data-v-7330c60c]:after,[data-v-7330c60c]:before {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

a[data-v-7330c60c]:active,a[data-v-7330c60c]:focus {
    outline: none
}

a[data-v-7330c60c],a[data-v-7330c60c]:focus,a[data-v-7330c60c]:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none
}

div[data-v-7330c60c]:focus {
    outline: none
}

.button[data-v-7330c60c] {
    cursor: pointer
}

img[data-v-7330c60c] {
    pointer-events: none;
    max-width: 100%
}

#nprogress .bar[data-v-7330c60c] {
    height: .08rem!important;
    background-color: #ff5d7d!important
}

#nprogress .bar .peg[data-v-7330c60c] {
    -webkit-box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d;
    box-shadow: 0 0 .26667rem #ff5d7d,0 0 .05333rem #ff5d7d
}

#nprogress .spinner-icon[data-v-7330c60c] {
    width: .58667rem!important;
    height: .58667rem!important;
    border: .08rem solid transparent!important;
    border-left-color: #ff5d7d!important;
    border-top-color: #ff5d7d!important
}

p[data-v-7330c60c] {
    display: block;
    -webkit-margin-before: 1em;
    margin-block-start:1em;-webkit-margin-after: 1em;
    margin-block-end:1em;-webkit-margin-start: 0;
    margin-inline-start:0;-webkit-margin-end: 0;
    margin-inline-end:0}

.cnt2[data-v-7330c60c] {
    min-height: 100vh;
    width: 100%;
    overflow: hidden;
    font-size: .37333rem
}

.my-swipe[data-v-7330c60c] {
    background-image: url(../../static-05301403/img/MBTI_text.49f8a374.png);
    background-position: 0 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 2.13333rem;
    border-radius: .24rem;
    font-size: .32rem;
    width: 90%;
    margin-left: 5%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: left
}

.swipe-item[data-v-7330c60c] {
    width: 8rem;
    line-height: .53333rem
}

.tab-view[data-v-7330c60c] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    color: #333;
    background-color: #fff;
    width: 90%;
    margin-left: 5%;
    border-radius: .4rem .4rem 0 0;
    overflow: hidden;
    font-weight: 400;
    font-size: .45333rem;
    color: #000
}

.tab-view .tab-item[data-v-7330c60c],.tab-view[data-v-7330c60c] {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.tab-view .tab-item[data-v-7330c60c] {
    display: block;
    width: 50%;
    height: 1.6rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative
}

.tab-view .tab-item .left_item[data-v-7330c60c] {
    position: absolute;
    top: .34667rem
}

.tab-view .bottom_line[data-v-7330c60c] {
    background-color: #2d9dec;
    height: .08rem;
    width: .72rem;
    margin: .08rem auto 0 auto;
    border-radius: .26667rem
}

.tab-view .color-9da1ad[data-v-7330c60c] {
    color: #9da1ad
}

.tab-view .fw5[data-v-7330c60c] {
    font-weight: 500
}

.tab-view .active[data-v-7330c60c] {
    color: #2d9dec;
    font-weight: 600
}

.tab-view .mt-34[data-v-7330c60c] {
    margin-top: .90667rem
}

.activeImg-left[data-v-7330c60c] {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAq0AAAB0CAYAAACv8gtlAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFT0lEQVR4nO3d2W4UVxSG0b96tMGYxAwiyfu/WkgQgjjgeOihclGgEMU4Hk5VnW6vJVkYc7Ovtj7Kp081bdu2AdhT2zbZJllvk6ttsmqTi033ZfkB7I5GtAKPUZsuXM/Wyed1srYJAaomWgHSxevHVReyANRHtAJ843yTfLjq/gSgHqIV4Bqf18n7S8cGAGohWgG+Y9smv192RwcAGJdoBfgfp6vuqatlCTAe0QpwC+eb5O1F9/QVgOGJVoBbutwmv54nG1sTYHCTsQcA2BXLSfLTQTJpxp4E4PERrQB3cDDtwlW3AgxLtALc0eE0ebkcewqAx0W0AtzD83lyNBt7CoDHQ7QC3NPrZTJzTgBgEKIV4J4mjWMCAEMRrQAPcDTrzrgC0C/RCvBAJ4uxJwDYf6IV4IEOp91VWAD0R7QCFPDjfOwJAPabaAUo4MksmbpJAKA3ohWggCbJM/e2AvRGtAIU4mUDAP0RrQCFHEy7u1sBKE+0AhR0YKsC9MJ6BSho6eorgF6IVoCC5o4HAPRCtAIUtLBVAXphvQIUNLNVAXphvQIUZKkC9MN+BSjIlVcA/RCtAABUT7QCAFA90QoAQPVEKwAA1ROtAABUT7QCAFA90QoAQPVEKwAA1ROtAABUT7QCAFA90QoAQPVEKwAA1ROtAABUT7QCFNaMPQDAHhKtAIXNbVaA4qxWgMJEK0B5VitAYXPnAwCKE60AhS2nY08AsH9EK0BhU09aAYoTrQCFOR4AUJ5oBShsNnHtFUBpohWgsCZuEAAozVoF6MHCdgUoyloF6IEnrQBlWasAPfCkFaAsaxWgB0vbFaAoaxWgB4uJ+1oBShKtAD058GYsgGJEK0BPDkUrQDGiFaAnohWgHNEK0JPlJJk51wpQhGgF6NHT2dgTAOwH0QrQoyPRClCEaAXo0eHUEQGAEkQrQM+ezceeAGD3iVaAnh07IgDwYKIVoGfzSfLE9VcADyJaAQbww2LsCQB2m2gFGMCTaXdvKwD3Y4UCDOTlcuwJAHaXaAUYyOHUywYA7ku0AgzoxSJxbSvA3YlWgAEtJsmxe1sB7ky0AgzsxaK7BguA27M2AQY2aZLXPpQFcCeiFWAEh9PkxN2tALcmWgFGcrLwpiyA2xKtACN6c9B9OAuAm1mVACOaNMnPh8nMPVgANxKtACObNckvwhXgRqIVoALzSReursICuF7Ttm079hAAdNZt8vY8udyOPQlAXUQrQGW2bfLuMvm8HnsSgHqIVoBKna6S95eJJQ0gWgGqdrlN3l04LgAgWgEq1yb54yr5uOqODgA8RqIVYEes2+TDVfLnauxJAIYnWgF2zNU2+XjVfVDLAgceC9EKsKNW2+7DWp/WycYmB/acaAXYcW26p66fVslfm7GnAeiHaAXYI5s2OVsnZ5vkfOODW8D+EK0Ae6pNd/71fJNcfIlYxwiAXSVaAR6R1bYL2a9fqzZZb7ubCQBqJloBACiuTdK23X+Kr7bdS1K+/ubnPkQrAACDWbfdB0dPV3f7LY9oBQBgcG26l6V8uLrdeXvRCgDAaDZt8v6yu3P6JqIVAIDRna66eP1emIpWAACqcLZOfru4Plwng08DAADXeDpL3hwkzTX/JloBAKjG01nyavnfn4tWAACqcjxPjmb//ploBQCgOq+WyfSbcwKiFQCA6kyb5GTxz99FKwAAVTqeJ7MvT1tFKwAAVWqSPJ9334tWAACq9Uy0AgBQu1mTHExFKwAAlTsUrQAA1G45Ea0AAFRuIVoBAKjdtBGtAABUbiJaAQCoXRPRCgDADpiNPQDAvtm2Y08AsH/+BpLrOBNTv8HRAAAAAElFTkSuQmCC);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%
}

.activeImg-right[data-v-7330c60c] {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAq0AAAB0CAYAAACv8gtlAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFDklEQVR4nO3dyXIbNxRA0Qf0RGqyJSuO7fz/V+Uzso+9gO2K40kSAQIkz6li9Rarp1sQGp3+/ufjxwDgJKSI2E3lt6SINUfMOSJHRE69VwfQztx7AQD82pwibuaI67nEqjYFLpFoBRjUboq4X0qsAlw6oxBgMPsp4mEtTwAK0QowiDlFPG7lKAAA3zIaAQZwPUf8uXmZCuBnRCtARynK7uqrpfdKAMYmWgE6ySni/c7ZVYCnEK0AHUwp4sM+Ysu9VwJwGoxLgCP7ssMqWAGezsgEOKIUJVh3jgQAPItoBTiix80ZVoCXEK0AR3IzuyUA4KVEK8ARTCni7dZ7FQCnS7QCHMGjDwcAHES0AjS2nyJuXTAIcBDRCtDYw9p7BQCnT7QCNLSb3BYAUINoBWjo3m0BAFWIVoBG5hRx7SwrQBWiFaCRG8EKUI1oBWhEtALUI1oBGsipvIQFQB2iFaCBnekKUJWxCtDAZpcVoCrRCtDA4pOtAFWJVoAGVtMVoCpjFaCB2XQFqMpYBWjAcAWoy1wFaCA70wpQlWgFAGB4ohUAgOGJVgAAhidaAQAYnmgFAGB4ohUAgOGJVgAAhidaAQAYnmgFAGB4ohUAgOGJVgAAhidaAQAYnmgFAGB4ohWgstR7AQBnSLQCVLaYrADVGa0AlYlWgPqMVoDKFucDAKoTrQCVbVPvFQCcH9EKUNlkpxWgOtEKUJnjAQD1iVaAilJEzCYrQHVGK0BFS3ZPK0ALohWgItddAbRhvAJUtJqqAE0YrwAViVaANoxXgIo2UxWgCeMVoJKc7LQCtGK8AlSyM1EBmjFiASq5mnuvAOB8iVaASvZT7xUAnC/RClDBnLyEBdCSEQtQgaMBAG2JVoAKbkUrQFOiFeBAc3KeFaA10QpwoNul9woAzp9oBTjQnaMBAM2JVoADXE0Ri0kK0JxRC3CA12vvFQBcBtEK8EJbLjutALQnWgFe6HHrvQKAyyFaAV7genbNFcAxiVaAZ0oR8cZZVoCjEq0Az3S3RKymJ8BRGbsAz7Bku6wAPYhWgGd4u0Xk1HsVAJdHtAI80cPq5SuAXkQrwBNcTSVaAehDtAL8xpoj3u16rwLgsolWgF+YU8SHvXOsAL2JVoCfmFPEX/vyBKCvufcCAEa05IgPu/IEoD/RCvA/W454b4cVYCiiFeA/bmZ3sQKMSLQCRESKiDdbxOul90oA+BHRCly8LUe83ZUnAGMSrcDFyinifol4vZadVgDGJVqBi3S3lC9cedkK4DSIVuBipCgvWt2v5StXAJwO0QqcvSlF3M4Rrxb3rgKcKtEKnK2rKeJ2KburTgEAnDbRCpyNnCL2U8T1FHE9lx1WAM6DaAVO1vQ5UndTea7ZjirAuRKtwPDmFDHniCWVMP3ycz4V4HK8OFq/7Gxsn/94zCkiJbscAADU96xonVN5+/Z2cbchAADH86RonVK5hPtusZMKAMDx/TZab+aIPzZv4QIA0M9PozVFxONWjgMAAEBPP4zWFBHvduWeQwAA6O27C2MEKwAAo/kuWh83wQoAwFi+idab2RlWAADG8zVap1RuCQAAgNF8jdaH1bVWAACMKUeUr1vdORYAAMCgckQ5x2qTFQCAUeWIiFu7rAAADCzvpnI8AAAARpX3U+8lAADAr+Xtu88LAADAWPIqWgEAGFx2NysAAKPLWbQCADA4zQoAwPCcaAUAYHjzvx97LwHg/Pg3FkBdnwB6tRGtATs8mQAAAABJRU5ErkJggg==);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100% 100%
}

.mb-20[data-v-7330c60c] {
    margin-bottom: .53333rem;
    width: 90%;
    margin-left: 5%;
    background: #fff;
    position: relative;
    border-radius: 0 0 .4rem .4rem;
    overflow: hidden
}

.left_item1[data-v-7330c60c] {
    width: 100%;
    height: 13.06667rem;
    background-position: 0 0;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-color: #fff
}

.mbti_cont[data-v-7330c60c] {
    background: url(../../static-05301403/img/img_bg.87018e00.png) top no-repeat;
    width: 100%;
    height: auto;
    background-size: cover;
    overflow: hidden
}

.mbti_top[data-v-7330c60c] {
    text-align: center;
    color: #fff;
    font-size: .53333rem
}

.mbti_top p[data-v-7330c60c] {
    margin: 0
}

.mbti_top img[data-v-7330c60c] {
    width: 95%;
    display: block;
    margin: 0 auto
}

.mbti_top .mbti_title1[data-v-7330c60c] {
    padding: 0 .13333rem .26667rem .13333rem
}

.mbti_top .mbti_title2[data-v-7330c60c] {
    margin-top: -.26667rem;
    font-size: .48rem;
    color: #2d9dec
}

.mbti_top .mbti_title[data-v-7330c60c] {
    margin-top: .8rem;
    margin-bottom: .66667rem;
    color: #a761e2;
    font-size: .53333rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-weight: 600
}

.mbti_top .mbti_title .ioc_left[data-v-7330c60c] {
    margin-right: .13333rem
}

.mbti_top .mbti_title .ioc_right[data-v-7330c60c] {
    margin-left: .13333rem
}

.pl-18[data-v-7330c60c] {
    width: 90%;
    margin-left: 5%
}

.x-index-header-title[data-v-7330c60c] {
    color: #b5c8ff
}

.mb-25[data-v-7330c60c] {
    margin-bottom: .66667rem
}

.font-18[data-v-7330c60c] {
    font-size: .48rem
}

.list[data-v-7330c60c] {
    border-radius: 0 0 .26667rem .26667rem;
    overflow: hidden;
    padding-bottom: .4rem;
    background: #fff
}

.flex[data-v-7330c60c] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.x-comment-list__item[data-v-7330c60c] {
    padding: .4rem .42667rem .53333rem .42667rem
}

.img-list-avatar[data-v-7330c60c] {
    width: 1.2rem;
    height: 1.2rem
}

.br-100[data-v-7330c60c] {
    border-radius: 100%;
    overflow: hidden
}

.flex-shrink-0[data-v-7330c60c] {
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.dark-gray[data-v-7330c60c] {
    color: #333;
    margin-bottom: .05333rem
}

.place-center[data-v-7330c60c] {
    place-items: center
}

.white[data-v-7330c60c] {
    color: #fff
}

.grid[data-v-7330c60c] {
    display: grid
}

.items-center[data-v-7330c60c] {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.x-list-kol-tag[data-v-7330c60c] {
    background-color: #fff5f7;
    background-image: url(https://res.vkunshan.com/static/home/<USER>
    background-position: left .21333rem top .08rem;
    background-size: .37333rem .26667rem;
    background-repeat: no-repeat;
    line-height: .42667rem;
    padding: 0 .21333rem 0 .69333rem;
    border-radius: .26667rem
}

.color-777e95[data-v-7330c60c] {
    color: #8c8c8c
}

.lh-22[data-v-7330c60c] {
    line-height: .58667rem
}

.font-10[data-v-7330c60c] {
    font-size: .26667rem
}

.mt-14[data-v-7330c60c] {
    margin-top: .37333rem
}

.font-13[data-v-7330c60c] {
    font-size: .34667rem
}

.color-ffa0b0[data-v-7330c60c] {
    color: #ffa0b0
}

.ml1[data-v-7330c60c] {
    margin-left: .26667rem
}

.x-list-img__item[data-v-7330c60c] {
    width: 2.13333rem;
    height: 3.30667rem;
    border-radius: .05333rem;
    pointer-events: auto
}

.mt-12[data-v-7330c60c] {
    margin-top: .58667rem
}

.mt-13[data-v-7330c60c] {
    margin-top: .37333rem
}

.mt-15[data-v-7330c60c] {
    margin-top: .53333rem
}

.flex-wrap[data-v-7330c60c] {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.x-comment-list__goods[data-v-7330c60c] {
    -webkit-box-shadow: .08rem .02667rem .26667rem rgba(205,207,209,.5);
    box-shadow: .08rem .02667rem .26667rem rgba(205,207,209,.5);
    border-radius: .10667rem;
    padding: .26667rem
}

.x-comment-list__goods-title[data-v-7330c60c] {
    font-size: .37333rem;
    color: #333;
    font-weight: 500;
    margin-bottom: .26667rem
}

.tab_bottom_line[data-v-7330c60c] {
    width: 100%;
    background: rgba(205,207,209,.5);
    height: .02667rem;
    margin-top: .93333rem
}

.img-logo[data-v-7330c60c] {
    width: 1.33333rem;
    height: 1.33333rem;
    margin: .26667rem;
    display: inline-block;
    overflow: hidden;
    position: relative
}

.ml-auto[data-v-7330c60c] {
    font-size: .53333rem;
    color: #c5c6c9;
    margin-left: 15%
}

.font-15[data-v-7330c60c] {
    font-size: .4rem
}

.color-5f667b[data-v-7330c60c] {
    color: #313131
}

.font-11[data-v-7330c60c] {
    font-size: .29333rem
}

.bg-f2f2ff[data-v-7330c60c] {
    background-color: #f2f2ff
}

.pl2[data-v-7330c60c] {
    padding-left: .21333rem
}

.pr2[data-v-7330c60c] {
    padding-right: .21333rem
}

.mr1[data-v-7330c60c] {
    margin-right: .10667rem
}

.icon-star[data-v-7330c60c] {
    width: .26667rem;
    height: .26667rem
}

.fw4[data-v-7330c60c] {
    font-weight: 400
}

.color-a18cfd[data-v-7330c60c] {
    color: #a18cfd
}

.x-content-bottom-tag[data-v-7330c60c] {
    height: .48rem;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    border-radius: .26667rem
}

.justify-center[data-v-7330c60c] {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.x-list-constellation[data-v-7330c60c] {
    background: -webkit-gradient(linear,right top,left top,color-stop(10%,#4facfe),to(#00f2fe));
    background: linear-gradient(270deg,#4facfe 10%,#00f2fe);
    padding: .02667rem .21333rem;
    border-radius: .21333rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.mr-10[data-v-7330c60c] {
    margin-right: .26667rem
}

.mt1[data-v-7330c60c] {
    margin-top: .10667rem
}

.bg-white[data-v-7330c60c] {
    background-color: #fff
}

.flex-column[data-v-7330c60c] {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.two[data-v-7330c60c] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: space-evenly;
    -ms-flex-pack: space-evenly;
    justify-content: space-evenly;
    margin-top: .53333rem;
    width: 100%
}

.two .cur[data-v-7330c60c] {
    border: .08rem solid #ff5d7d!important;
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1)
}

.two .cont[data-v-7330c60c] {
    padding: .4rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: .26667rem;
    width: 37%;
    border: .08rem solid rgba(255,0,0,0);
    text-align: center;
    -webkit-box-shadow: 0 .08rem .3rem 0 #a4beca;
    box-shadow: 0 .08rem .3rem 0 #a4beca;
    background: #fff
}

.cur2[data-v-7330c60c] {
    display: none
}

.bottom_text[data-v-7330c60c] {
    width: 90%;
    height: 1.86667rem;
    background: #fff;
    margin-left: 5%
}

.bt_bottom[data-v-7330c60c] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-top: .4rem
}

.two .img1[data-v-7330c60c] {
    display: block;
    width: .48rem;
    height: 100%;
    margin-right: .13333rem
}

.two .img1 img[data-v-7330c60c] {
    pointer-events: none;
    max-width: 90%
}

.two .cont .img[data-v-7330c60c] {
    width: 100%
}

.two .cont .bt1[data-v-7330c60c] {
    font-size: .45333rem;
    font-weight: 500
}

.two .cont p[data-v-7330c60c] {
    color: #999;
    letter-spacing: 0
}

.page_detail .detail_title_ydrated[data-v-7330c60c] {
    text-align: center;
    font-weight: 700;
    font-size: .48rem;
    margin-bottom: .26667rem
}

.page_detail .detail_txt___3QorP[data-v-7330c60c] {
    line-height: .61333rem
}

.page_detail[data-v-7330c60c] {
    width: 90%;
    background-color: #e6f9ff;
    border-radius: .26667rem;
    color: #308fa6;
    margin: 0 auto;
    margin-top: .26667rem;
    padding: .4rem
}

.page_detail[data-v-7330c60c] h5 {
    text-align: center;
    font-weight: 700;
    font-size: .48rem;
    margin-bottom: .53333rem
}

.page_detail[data-v-7330c60c] p {
    line-height: .61333rem;
    font-size: .37333rem;
    margin-bottom: .26667rem
}

.mbtibtn[data-v-7330c60c] {
    margin-left: 50%;
    margin-top: 1.33333rem;
    -webkit-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background: url(../../static-05301403/img/cs_bth.42fe96af.png) top no-repeat;
    border-radius: 1.2rem;
    line-height: 1.2rem;
    font-weight: 700;
    font-size: .48rem;
    height: 1.06667rem;
    width: 4.8rem;
    border-radius: 1.06667rem;
    color: #fff;
    line-height: 1.06667rem;
    text-align: center;
    letter-spacing: .13333rem
}

.tips[data-v-7330c60c] {
    padding: .26667rem .53333rem .4rem;
    color: #fff;
    font-size: .37333rem
}

.tips2[data-v-7330c60c] {
    padding-bottom: .53333rem;
    color: #fff;
    font-size: .32rem;
    text-align: center
}

@-webkit-keyframes scaleAnimation-data-v-9a11e5a2-7330c60c {
    0% {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }

    50% {
        -webkit-transform: translate(-50%,-50%) scale(1.1);
        transform: translate(-50%,-50%) scale(1.1)
    }

    to {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }
}

@keyframes scaleAnimation-data-v-9a11e5a2-7330c60c {
    0% {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }

    50% {
        -webkit-transform: translate(-50%,-50%) scale(1.1);
        transform: translate(-50%,-50%) scale(1.1)
    }

    to {
        -webkit-transform: translate(-50%,-50%) scale(1);
        transform: translate(-50%,-50%) scale(1)
    }
}

.m[data-v-7330c60c] {
    margin-top: -.4rem;
    width: 100%
}

.m img[data-v-7330c60c] {
    width: 100%;
    display: block;
    padding: 0;
    margin: 0
}
