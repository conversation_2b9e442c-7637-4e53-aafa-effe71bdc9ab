<?php if (!defined('THINK_PATH')) exit(); /*a:1:{s:98:"F:\PHPCUSTOM\wwwroot\web1.mbtiexplorer.com\public/../application/mbti/view/default/index\main.html";i:1751898629;}*/ ?>
<html class=" " style="font-size: 40px;">
<head>
    <meta charSet="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0,viewport-fit=cover"
          name="viewport">
    <meta name="description" content="专业心理测评">
    <meta name="referrer" content="origin-when-cross-origin">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <title>MBTI测试2025官方版</title>
    <style>.index-loading {
        width: 120px;
        height: 120px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        border-radius: 10px;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        font-size: 16px;
    }

    .index-loading-image {
        text-align: center;

        width: 40px;
        height: 40px;
        margin-bottom: 0.21333rem;
    }</style>
  
 

    <link href="/Public/mbit/newindex_files/chunk-10183080.3ad9ab75.css" rel="prefetch">



    <link href="/Public/mbit/newindex_files/chunk-18ebcd69.acfc28c4.css" rel="prefetch">

    <link href="/Public/mbit/newindex_files/chunk-3128d268.e02558e5.css" rel="prefetch">
   
    <link href="/Public/mbit/newindex_files/chunk-c13a5f6e.fbcd3cb8.css" rel="prefetch">
  
    <link href="/Public/mbit/newindex_files/chunk-commons.468be780.css" rel="prefetch">
  
    

    <link href="/Public/mbit/newindex_files/chunk-libs.d3f5faf3.css" rel="preload" as="style">
    <link href="/Public/mbit/newindex_files/chunk-vant.53a6ffb4.css" rel="preload" as="style">
   
    <link href="/Public/mbit/newindex_files/chunk-vant.53a6ffb4.css" rel="stylesheet">
    <link href="/Public/mbit/newindex_files/chunk-libs.d3f5faf3.css" rel="stylesheet">


    <link href="/Public/mbit/newindex_files/app.15d98991.css" rel="preload" as="style">
    <link href="/Public/mbit/newindex_files/app.15d98991.css" rel="stylesheet">
    
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-commons.468be780.css">
   
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-18ebcd69.acfc28c4.css">
   
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-10183080.3ad9ab75.css">
 
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-c13a5f6e.fbcd3cb8.css">

    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-3128d268.e02558e5.css">

    <style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        z-index: 2147483647;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .immersive-translate-attach-loading::after {
        content: " ";

        --loading-color: #f78fb6;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        display: block;
        margin: 12px auto;
        position: relative;
        color: white;
        left: -100px;
        box-sizing: border-box;
        animation: immersiveTranslateShadowRolling 1.5s linear infinite;

        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-2000%, -50%);
        z-index: 100;
    }

    .immersive-translate-loading-spinner {
        vertical-align: middle !important;
        width: 10px !important;
        height: 10px !important;
        display: inline-block !important;
        margin: 0 4px !important;
        border: 2px rgba(221, 244, 255, 0.6) solid !important;
        border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
        border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
        border-radius: 50% !important;
        padding: 0 !important;
        -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
        animation: immersive-translate-loading-animation 0.6s infinite linear !important;
    }

    @-webkit-keyframes immersive-translate-loading-animation {
        from {
            -webkit-transform: rotate(0deg);
        }

        to {
            -webkit-transform: rotate(359deg);
        }
    }

    @keyframes immersive-translate-loading-animation {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(359deg);
        }
    }

    .immersive-translate-input-loading {
        --loading-color: #f78fb6;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        display: block;
        margin: 12px auto;
        position: relative;
        color: white;
        left: -100px;
        box-sizing: border-box;
        animation: immersiveTranslateShadowRolling 1.5s linear infinite;
    }

    @keyframes immersiveTranslateShadowRolling {
        0% {
            box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }

        12% {
            box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }

        25% {
            box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }

        36% {
            box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
            100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
        }

        50% {
            box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
            110px 0 var(--loading-color), 100px 0 var(--loading-color);
        }

        62% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
            120px 0 var(--loading-color), 110px 0 var(--loading-color);
        }

        75% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
            130px 0 var(--loading-color), 120px 0 var(--loading-color);
        }

        87% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
            200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
        }

        100% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
            200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
        }
    }

    .immersive-translate-toast {
        display: flex;
        position: fixed;
        z-index: 2147483647;
        left: 0;
        right: 0;
        top: 1%;
        width: fit-content;
        padding: 12px 20px;
        margin: auto;
        overflow: auto;
        background: #fef6f9;
        box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
        font-size: 15px;
        border-radius: 8px;
        color: #333;
    }

    .immersive-translate-toast-content {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .immersive-translate-toast-hidden {
        margin: 0 20px 0 72px;
        text-decoration: underline;
        cursor: pointer;
    }

    .immersive-translate-toast-close {
        color: #666666;
        font-size: 20px;
        font-weight: bold;
        padding: 0 10px;
        cursor: pointer;
    }

    @media screen and (max-width: 768px) {
        .immersive-translate-toast {
            top: 0;
            padding: 12px 0px 0 10px;
        }

        .immersive-translate-toast-content {
            flex-direction: column;
            text-align: center;
        }

        .immersive-translate-toast-hidden {
            margin: 10px auto;
        }
    }

    .immersive-translate-modal {
        display: none;
        position: fixed;
        z-index: 2147483647;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgb(0, 0, 0);
        background-color: rgba(0, 0, 0, 0.4);
        font-size: 15px;
    }

    .immersive-translate-modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 40px 24px 24px;
        border: 1px solid #888;
        border-radius: 10px;
        width: 80%;
        max-width: 270px;
        font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
        "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
        "Segoe UI Symbol", "Noto Color Emoji";
        position: relative;
    }

    @media screen and (max-width: 768px) {
        .immersive-translate-modal-content {
            margin: 50% auto !important;
        }
    }

    .immersive-translate-modal .immersive-translate-modal-content-in-input {
        max-width: 500px;
    }

    .immersive-translate-modal-content-in-input .immersive-translate-modal-body {
        text-align: left;
        max-height: unset;
    }

    .immersive-translate-modal-title {
        text-align: center;
        font-size: 16px;
        font-weight: 700;
        color: #333333;
    }

    .immersive-translate-modal-body {
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        word-break: break-all;
        margin-top: 24px;
    }

    @media screen and (max-width: 768px) {
        .immersive-translate-modal-body {
            max-height: 250px;
            overflow-y: auto;
        }
    }

    .immersive-translate-close {
        color: #666666;
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 20px;
        font-weight: bold;
    }

    .immersive-translate-close:hover,
    .immersive-translate-close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }

    .immersive-translate-modal-footer {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-top: 24px;
    }

    .immersive-translate-btn {
        width: fit-content;
        color: #fff;
        background-color: #ea4c89;
        border: none;
        font-size: 16px;
        margin: 0 8px;
        padding: 9px 30px;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .immersive-translate-btn:hover {
        background-color: #f082ac;
    }

    .immersive-translate-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .immersive-translate-btn:disabled:hover {
        background-color: #ea4c89;
    }

    .immersive-translate-cancel-btn {
        /* gray color */
        background-color: rgb(89, 107, 120);
    }

    .immersive-translate-cancel-btn:hover {
        background-color: hsl(205, 20%, 32%);
    }

    .immersive-translate-action-btn {
        background-color: transparent;
        color: #ea4c89;
        border: 1px solid #ea4c89;
    }

    .immersive-translate-btn svg {
        margin-right: 5px;
    }

    .immersive-translate-link {
        cursor: pointer;
        user-select: none;
        -webkit-user-drag: none;
        text-decoration: none;
        color: #007bff;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .immersive-translate-primary-link {
        cursor: pointer;
        user-select: none;
        -webkit-user-drag: none;
        text-decoration: none;
        color: #ea4c89;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    .immersive-translate-modal input[type="radio"] {
        margin: 0 6px;
        cursor: pointer;
    }

    .immersive-translate-modal label {
        cursor: pointer;
    }

    .immersive-translate-close-action {
        position: absolute;
        top: 2px;
        right: 0px;
        cursor: pointer;
    }

    .imt-image-status {
        background-color: rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 16px !important;
    }

    .imt-image-status img,
    .imt-image-status svg,
    .imt-img-loading {
        width: 28px !important;
        height: 28px !important;
        margin: 0 0 8px 0 !important;
        min-height: 28px !important;
        min-width: 28px !important;
        position: relative !important;
    }

    .imt-img-loading {
        background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
        background-size: 28px 28px;
        animation: image-loading-rotate 1s linear infinite !important;
    }

    .imt-image-status span {
        color: var(--bg-2, #fff) !important;
        font-size: 14px !important;
        line-height: 14px !important;
        font-weight: 500 !important;
        font-family: "PingFang SC", Arial, sans-serif !important;
    }

    @keyframes image-loading-rotate {
        from {
            transform: rotate(360deg);
        }
        to {
            transform: rotate(0deg);
        }
    }

    .mbti_cont__button[data-v-2c9cfb8d] {
        margin: 1rem auto 0;
    }

    /* 弹框样式 */
    .mbti-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .mbti-modal-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 40px 30px 30px;
        max-width: 400px;
        width: 90%;
        position: relative;
        text-align: center;
        color: white;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .mbti-modal-close {
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 24px;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: color 0.3s ease;
    }

    .mbti-modal-close:hover {
        color: white;
    }

    .mbti-modal-avatar {
        width: 80px;
        height: 80px;
        background-color: white;
        border-radius: 50%;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mbti-modal-avatar svg {
        width: 40px;
        height: 40px;
        fill: #667eea;
    }

    .mbti-modal-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 15px;
    }

    .mbti-modal-description {
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 20px;
        opacity: 0.9;
    }

    .mbti-modal-features {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 25px;
        opacity: 0.8;
    }

    .mbti-modal-price {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 25px;
    }

    .mbti-modal-price-text {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .mbti-modal-price-amount {
        font-size: 20px;
        font-weight: bold;
        color: #ff6b9d;
    }

    .mbti-modal-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: 2px solid white;
        color: white;
        padding: 15px 40px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }

    .mbti-modal-button:hover {
        background: white;
        color: #667eea;
    }

    .main-content {
        display: none;
    }

    .main-content.show {
        display: block;
    }
    </style>
</head>
<body class="">
<!-- MBTI弹框 -->
<div id="mbtiModal" class="mbti-modal">
    <div class="mbti-modal-content">
        <span class="mbti-modal-close" onclick="closeMbtiModal()">&times;</span>
        <div class="mbti-modal-avatar">
            <svg viewBox="0 0 24 24">
                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
            </svg>
        </div>
        <div class="mbti-modal-title">MBTI十六型人格测试</div>
        <div class="mbti-modal-description">
            全球使用最广泛的人格类型测试工具，世界500强企业入职必测项目。能帮你了解自己到底是什么性格，清楚自己性格的优劣势。
        </div>
        <div class="mbti-modal-features">
            不管是更懂自己、和人打交道、谈恋爱交朋友、规划工作发展，都特别有用。
        </div>
        <div class="mbti-modal-price">
            <div class="mbti-modal-price-text">本次测试免费，获取专属性格解析报告</div>
            <div class="mbti-modal-price-amount">需付费 ¥3.9起</div>
        </div>
        <button class="mbti-modal-button" onclick="startMbtiTest()">开始测试</button>
    </div>
</div>

<div id="app" data-v-app="">
    <section class="container min-height main-content"  data-v-766d0ce6="" style="--container-min-height: 898px;">
        <div data-v-4d303239="" data-v-766d0ce6="" style=""><!---->
            <div class="cnt2" data-v-2c9cfb8d="" data-v-4d303239="" style="height: var(--container-min-height);">
                <div data-v-2c9cfb8d="">
                    <div class="mbti_cont" data-v-2c9cfb8d="">
                        <div class="mbti_top" data-v-2c9cfb8d=""><img
                                src="/Public/mbit/newindex_files/8e3e5fc3485e5e9b329e8ae96d278ca5.png"
                                alt="" data-v-2c9cfb8d="">
                            <div class="mbti_top_two" data-v-2c9cfb8d=""><p class="mbti_title2"
                                                                                data-v-2c9cfb8d="">
                                <p>
                                    <img src="/Public/mbit/newindex_files/e766a579b1ce86eb0ec0412c8b93b5c8.png"
                                         title=""
                                         _src="/Public/mbit/newindex_files/e766a579b1ce86eb0ec0412c8b93b5c8.png"
                                         alt=""></p></p>
                                <div class="two" data-v-2c9cfb8d="">
                                    <div class="cont" data-v-2c9cfb8d="" style="" onclick="toggleContSelection(this)"><!----><img
                                            src="/Public/mbit/newindex_files/029f5031d447bf18421fc9abb400036c.png"
                                            alt="" class="img" data-v-2c9cfb8d=""></div>
                                    <div class="cont cur" data-v-2c9cfb8d=""
                                         style="border-color: rgb(10, 189, 96);" onclick="toggleContSelection(this)">
                                        <div class="img1" data-v-2c9cfb8d=""
                                             style="background-color: rgb(10, 189, 96);"><img
                                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAVCAYAAAC33pUlAAAACXBIWXMAAC4jAAAuIwF4pT92AAABgklEQVRIibXWvWoUURgG4HfXYBOr+NNoEbDQCGojAUGEoCQ3IKRR8RJEvAAbwUKsxEYIxM5WNIUggkQRr8BOCAEhlT8hjeJjMTu4bubsbnY3L0wxzPnmOT8zc6aF7EPaSaaTzCWZT3I2yfup/ZCSLCS50YFmkhxLkmBSRxtX8AY/8Me/fMXSJJAWTuM5ftud15jF2CM7iFvYakB+YaW7/TjQETxqQOA77vbWjAqdxJr/16XOJpYxVcLu4BVWMT8AmsOHwog+Y0G1jrtqg6c9PfyGa4WCUwOgC/06mkLhNhZ7wOOqJ6spX3CmH1Rjzwo32MKJTsNpPCm028D5QVCNtZWn5m1ndMuF6zu4OgxUY8FR1VQ05Z5qHZtyc1ioG2thSfMXoJT7ODAKVn8NHgwJrWFmL1AvVr+sHwdAmzi3V6gJC67jZx/s9ihQCQteFqAXODRp7JJqD+rOBi6PCiHtwk67nmQlyXbnfCfJapJ342zf/X4LHiY5nORikk9JHo8DJclfs+0FX3MjOVMAAAAASUVORK5CYII="
                                                alt="" data-v-2c9cfb8d=""></div>
                                        <img src="/Public/mbit/newindex_files/e4d4f187d7ba32f8ffe59093b575fab0.png"
                                             alt="" class="img" data-v-2c9cfb8d=""></div>
                                </div>
                                <div class="button mbti_cont__button" data-v-2c9cfb8d="" onclick="handleMbtiButtonClick()"> 开始测试</div>
                            </div>
                        </div>
                    </div>
                    <div class="tips tips-mt" data-v-2c9cfb8d=""><p><img style="width:100%"
                                                                             src="/Public/mbit/newindex_files/30fd5d1cec831b9a4544ce084632e7b7.png"
                                                                             title=""
                                                                             _src="/Public/mbit/newindex_files/30fd5d1cec831b9a4544ce084632e7b7.png"
                                                                             alt=""></p></div><!----></div><!----></div>
            <!----><!----><!----></div>
    </section><!----><!----><!----><!----><!----></div>
<script>
    localStorage.setItem("repaynum", 0);
    localStorage.setItem('discountApplied', 'false');
    if (!!window.ActiveXObject || "ActiveXObject" in window) {
    document.write(
        "<h3>检测到您当前使用的是IE浏览器，本站暂不支持IE浏览器访问，请更换其他浏览器</h3>"
    );
}

// 解决ios10以上不兼容meta标签禁止缩放功能
window.onload = function () {
    // 阻止双击放大
    var lastTouchEnd = 0;
    document.addEventListener("touchstart", function (event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    });
    document.addEventListener(
        "touchend",
        function (event) {
            var now = new Date().getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        },
        false
    );

    // 阻止双指放大
    document.addEventListener("gesturestart", function (event) {
        event.preventDefault();
    });
};


    function handleMbtiButtonClick() {
        window.location.href = '/mbti/index/question.html?type=1';
    }

    // 弹框控制函数
    function closeMbtiModal() {
        document.getElementById('mbtiModal').style.display = 'none';
        document.querySelector('.main-content').classList.add('show');
    }

    function startMbtiTest() {
        closeMbtiModal();
        // 可以在这里添加其他逻辑，比如滚动到测试区域
    }

    // 页面加载时显示弹框
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('mbtiModal').style.display = 'flex';
        document.querySelector('.main-content').classList.remove('show');
    });
</script>

</body>
<div id="immersive-translate-popup" style="all: initial"></div>
</html>

<script>
    function toggleContSelection(clickedElement) {
        const parent = clickedElement.parentElement;
        const contElements = parent.querySelectorAll('.cont');
        
        contElements.forEach(el => {
            el.classList.remove('cur');
            el.removeAttribute('style');
            const img1 = el.querySelector('.img1');
            if(img1) {
                img1.remove();
            }
        });
        
        clickedElement.classList.add('cur');
        clickedElement.style.borderColor = 'rgb(10, 189, 96)';
        
        const img1 = clickedElement.querySelector('.img1');
        if(img1) {
            img1.style.backgroundColor = 'rgb(10, 189, 96)';
        } else {
            const img1 = clickedElement.querySelector('.img');
            if(img1) {
                const newImg1 = document.createElement('div');
                newImg1.className = 'img1';
                newImg1.setAttribute('data-v-2c9cfb8d', '');
                newImg1.style.backgroundColor = 'rgb(10, 189, 96)';
                newImg1.innerHTML = '<div class="img1" data-v-2c9cfb8d=""   style="background-color: rgb(10, 189, 96);"><img  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAVCAYAAAC33pUlAAAACXBIWXMAAC4jAAAuIwF4pT92AAABgklEQVRIibXWvWoUURgG4HfXYBOr+NNoEbDQCGojAUGEoCQ3IKRR8RJEvAAbwUKsxEYIxM5WNIUggkQRr8BOCAEhlT8hjeJjMTu4bubsbnY3L0wxzPnmOT8zc6aF7EPaSaaTzCWZT3I2yfup/ZCSLCS50YFmkhxLkmBSRxtX8AY/8Me/fMXSJJAWTuM5ftud15jF2CM7iFvYakB+YaW7/TjQETxqQOA77vbWjAqdxJr/16XOJpYxVcLu4BVWMT8AmsOHwog+Y0G1jrtqg6c9PfyGa4WCUwOgC/06mkLhNhZ7wOOqJ6spX3CmH1Rjzwo32MKJTsNpPCm028D5QVCNtZWn5m1ndMuF6zu4OgxUY8FR1VQ05Z5qHZtyc1ioG2thSfMXoJT7ODAKVn8NHgwJrWFmL1AvVr+sHwdAmzi3V6gJC67jZx/s9ihQCQteFqAXODRp7JJqD+rOBi6PCiHtwk67nmQlyXbnfCfJapJ342zf/X4LHiY5nORikk9JHo8DJclfs+0FX3MjOVMAAAAASUVORK5CYII="     alt="" data-v-2c9cfb8d=""></div>';
                clickedElement.insertBefore(newImg1, img1);
            }
        }
    }
</script>