<?php if (!defined('THINK_PATH')) exit(); /*a:1:{s:98:"F:\PHPCUSTOM\wwwroot\web1.mbtiexplorer.com\public/../application/mbti/view/default/index\main.html";i:1751901222;}*/ ?>
<html class=" " style="font-size: 40px;">
<head>
    <meta charSet="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0,viewport-fit=cover"
          name="viewport">
    <meta name="description" content="专业心理测评">
    <meta name="referrer" content="origin-when-cross-origin">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">

    <title>MBTI测试2025官方版</title>
    <style>

    </style>
  
 

    <link href="/Public/mbit/newindex_files/chunk-10183080.3ad9ab75.css" rel="prefetch">



    <link href="/Public/mbit/newindex_files/chunk-18ebcd69.acfc28c4.css" rel="prefetch">

    <link href="/Public/mbit/newindex_files/chunk-3128d268.e02558e5.css" rel="prefetch">
   
    <link href="/Public/mbit/newindex_files/chunk-c13a5f6e.fbcd3cb8.css" rel="prefetch">
  
    <link href="/Public/mbit/newindex_files/chunk-commons.468be780.css" rel="prefetch">
  
    

    <link href="/Public/mbit/newindex_files/chunk-libs.d3f5faf3.css" rel="preload" as="style">
    <link href="/Public/mbit/newindex_files/chunk-vant.53a6ffb4.css" rel="preload" as="style">
   
    <link href="/Public/mbit/newindex_files/chunk-vant.53a6ffb4.css" rel="stylesheet">
    <link href="/Public/mbit/newindex_files/chunk-libs.d3f5faf3.css" rel="stylesheet">


    <link href="/Public/mbit/newindex_files/app.15d98991.css" rel="preload" as="style">
    <link href="/Public/mbit/newindex_files/app.15d98991.css" rel="stylesheet">
    
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-commons.468be780.css">
   
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-18ebcd69.acfc28c4.css">
   
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-10183080.3ad9ab75.css">
 
    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-c13a5f6e.fbcd3cb8.css">

    <link rel="stylesheet" type="text/css" href="/Public/mbit/newindex_files/chunk-3128d268.e02558e5.css">

    <style >

    .mbti_cont__button[data-v-2c9cfb8d] {
        margin: 1rem auto 0;
    }
    </style>
</head>
<body >
<div id="app" data-v-app="">
    <section class="container min-height"  data-v-766d0ce6="" style="--container-min-height: 898px;">
        <div data-v-4d303239="" data-v-766d0ce6="" style=""><!---->
            <div class="cnt2" data-v-2c9cfb8d="" data-v-4d303239="" style="height: var(--container-min-height);">
                <div data-v-2c9cfb8d="">
                    <div class="mbti_cont" data-v-2c9cfb8d="">
                        <div class="mbti_top" data-v-2c9cfb8d=""><img
                                src="/Public/mbit/newindex_files/8e3e5fc3485e5e9b329e8ae96d278ca5.png"
                                alt="" data-v-2c9cfb8d="">
                            <div class="mbti_top_two" data-v-2c9cfb8d=""><p class="mbti_title2"
                                                                                data-v-2c9cfb8d="">
                                <p>
                                    <img src="/Public/mbit/newindex_files/e766a579b1ce86eb0ec0412c8b93b5c8.png"
                                         title=""
                                         _src="/Public/mbit/newindex_files/e766a579b1ce86eb0ec0412c8b93b5c8.png"
                                         alt=""></p></p>
                                <div class="two" data-v-2c9cfb8d="">
                                    <div class="cont" data-v-2c9cfb8d="" style="" onclick="toggleContSelection(this)"><!----><img
                                            src="/Public/mbit/newindex_files/029f5031d447bf18421fc9abb400036c.png"
                                            alt="" class="img" data-v-2c9cfb8d=""></div>
                                    <div class="cont cur" data-v-2c9cfb8d=""
                                         style="border-color: rgb(10, 189, 96);" onclick="toggleContSelection(this)">
                                        <div class="img1" data-v-2c9cfb8d=""
                                             style="background-color: rgb(10, 189, 96);"><img
                                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAVCAYAAAC33pUlAAAACXBIWXMAAC4jAAAuIwF4pT92AAABgklEQVRIibXWvWoUURgG4HfXYBOr+NNoEbDQCGojAUGEoCQ3IKRR8RJEvAAbwUKsxEYIxM5WNIUggkQRr8BOCAEhlT8hjeJjMTu4bubsbnY3L0wxzPnmOT8zc6aF7EPaSaaTzCWZT3I2yfup/ZCSLCS50YFmkhxLkmBSRxtX8AY/8Me/fMXSJJAWTuM5ftud15jF2CM7iFvYakB+YaW7/TjQETxqQOA77vbWjAqdxJr/16XOJpYxVcLu4BVWMT8AmsOHwog+Y0G1jrtqg6c9PfyGa4WCUwOgC/06mkLhNhZ7wOOqJ6spX3CmH1Rjzwo32MKJTsNpPCm028D5QVCNtZWn5m1ndMuF6zu4OgxUY8FR1VQ05Z5qHZtyc1ioG2thSfMXoJT7ODAKVn8NHgwJrWFmL1AvVr+sHwdAmzi3V6gJC67jZx/s9ihQCQteFqAXODRp7JJqD+rOBi6PCiHtwk67nmQlyXbnfCfJapJ342zf/X4LHiY5nORikk9JHo8DJclfs+0FX3MjOVMAAAAASUVORK5CYII="
                                                alt="" data-v-2c9cfb8d=""></div>
                                        <img src="/Public/mbit/newindex_files/e4d4f187d7ba32f8ffe59093b575fab0.png"
                                             alt="" class="img" data-v-2c9cfb8d=""></div>
                                </div>
                                <div class="button mbti_cont__button" data-v-2c9cfb8d="" onclick="handleMbtiButtonClick()"> 开始测试</div>
                            </div>
                        </div>
                    </div>
                    <div class="tips tips-mt" data-v-2c9cfb8d=""><p><img style="width:100%"
                                                                             src="/Public/mbit/newindex_files/30fd5d1cec831b9a4544ce084632e7b7.png"
                                                                             title=""
                                                                             _src="/Public/mbit/newindex_files/30fd5d1cec831b9a4544ce084632e7b7.png"
                                                                             alt=""></p></div><!----></div><!----></div>
            <!----><!----><!----></div>
    </section><!----><!----><!----><!----><!----></div>
<script>
    localStorage.setItem("repaynum", 0);
    localStorage.setItem('discountApplied', 'false');
    if (!!window.ActiveXObject || "ActiveXObject" in window) {
    document.write(
        "<h3>检测到您当前使用的是IE浏览器，本站暂不支持IE浏览器访问，请更换其他浏览器</h3>"
    );
}

// 解决ios10以上不兼容meta标签禁止缩放功能
window.onload = function () {
    // 阻止双击放大
    var lastTouchEnd = 0;
    document.addEventListener("touchstart", function (event) {
        if (event.touches.length > 1) {
            event.preventDefault();
        }
    });
    document.addEventListener(
        "touchend",
        function (event) {
            var now = new Date().getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        },
        false
    );

    // 阻止双指放大
    document.addEventListener("gesturestart", function (event) {
        event.preventDefault();
    });
};


    function handleMbtiButtonClick() {
        window.location.href = '/mbti/index/question.html?type=1';
    }
</script>

</body>
</html>

<script>
    function toggleContSelection(clickedElement) {
        const parent = clickedElement.parentElement;
        const contElements = parent.querySelectorAll('.cont');
        
        contElements.forEach(el => {
            el.classList.remove('cur');
            el.removeAttribute('style');
            const img1 = el.querySelector('.img1');
            if(img1) {
                img1.remove();
            }
        });
        
        clickedElement.classList.add('cur');
        clickedElement.style.borderColor = 'rgb(10, 189, 96)';
        
        const img1 = clickedElement.querySelector('.img1');
        if(img1) {
            img1.style.backgroundColor = 'rgb(10, 189, 96)';
        } else {
            const img1 = clickedElement.querySelector('.img');
            if(img1) {
                const newImg1 = document.createElement('div');
                newImg1.className = 'img1';
                newImg1.setAttribute('data-v-2c9cfb8d', '');
                newImg1.style.backgroundColor = 'rgb(10, 189, 96)';
                newImg1.innerHTML = '<div class="img1" data-v-2c9cfb8d=""   style="background-color: rgb(10, 189, 96);"><img  src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAVCAYAAAC33pUlAAAACXBIWXMAAC4jAAAuIwF4pT92AAABgklEQVRIibXWvWoUURgG4HfXYBOr+NNoEbDQCGojAUGEoCQ3IKRR8RJEvAAbwUKsxEYIxM5WNIUggkQRr8BOCAEhlT8hjeJjMTu4bubsbnY3L0wxzPnmOT8zc6aF7EPaSaaTzCWZT3I2yfup/ZCSLCS50YFmkhxLkmBSRxtX8AY/8Me/fMXSJJAWTuM5ftud15jF2CM7iFvYakB+YaW7/TjQETxqQOA77vbWjAqdxJr/16XOJpYxVcLu4BVWMT8AmsOHwog+Y0G1jrtqg6c9PfyGa4WCUwOgC/06mkLhNhZ7wOOqJ6spX3CmH1Rjzwo32MKJTsNpPCm028D5QVCNtZWn5m1ndMuF6zu4OgxUY8FR1VQ05Z5qHZtyc1ioG2thSfMXoJT7ODAKVn8NHgwJrWFmL1AvVr+sHwdAmzi3V6gJC67jZx/s9ihQCQteFqAXODRp7JJqD+rOBi6PCiHtwk67nmQlyXbnfCfJapJ342zf/X4LHiY5nORikk9JHo8DJclfs+0FX3MjOVMAAAAASUVORK5CYII="     alt="" data-v-2c9cfb8d=""></div>';
                clickedElement.insertBefore(newImg1, img1);
            }
        }
    }
</script>